import 'package:gt_plus/models/meta_data_model.dart';
import 'package:gt_plus/models/provider_list_model.dart';
import 'package:gt_plus/models/clinic_details_model.dart';
import 'package:gt_plus/models/login_model.dart';
import 'package:gt_plus/services/api_service.dart';

/// Service to provide mock API responses when in test mode
class MockApiService {
  /// Get mock meta data for test mode
  static Map<String, dynamic> getMockMetaData() {
    return {
      "data": [
        {
          "tongueImage": {"completed": true, "show": true}
        },
        {
          "temperatureRecorded": {"completed": true, "show": true}
        },
        {
          "teethImage": {"completed": true, "show": true}
        },
        {
          "ppgRecorded": {"completed": true, "show": true}
        },
        {
          "palmImage": {"completed": true, "show": true}
        },
        {
          "faceMesh": {"completed": true, "show": true}
        },
        {
          "eyeRight": {"completed": true, "show": true}
        },
        {
          "eyeLeft": {"completed": true, "show": true}
        },
        {
          "cognivue": {"completed": true, "show": true}
        },
        {
          "bpRecorded": {"completed": true, "show": true}
        },
        {
          "basicDetails": {"completed": true, "show": true}
        },
        {
          "audiometry": {"completed": true, "show": true}
        },
        {
          "quickSin": {"completed": true, "show": true}
        },
        {
          "extraData": {"completed": true, "show": true}
        },
        {
          "skinImage": {"completed": false, "show": true}
        }
      ],
      "isDone": true
    };
  }

  /// Get a MetaDataModel instance from mock data
  static MetaDataModel getMockMetaDataModel() {
    final jsonData = getMockMetaData();
    return MetaDataModel.fromJson(jsonData);
  }

  /// Get mock providers data for test mode
  static Map<String, dynamic> getMockProviders() {
    return {
      "providers": [
        "Test Provider 1",
        "Test Provider 2", 
        "Test Provider 3",
        "Demo1",
        "Demo2",
        "Demo3",
        "Demo4",
        "Demo5"
      ]
    };
  }

  /// Get a ProviderListModel instance from mock data
  static ProviderListModel getMockProviderListModel() {
    final jsonData = getMockProviders();
    return ProviderListModel.fromJson(jsonData);
  }

  /// Get mock clinic details data for test mode
  static Map<String, dynamic> getMockClinicDetails() {
    return {
      "clinicName": "Test Clinic",
      "programName": "Test Program",
      "clinicLogo": "https://example.com/logo.png",
      "success": true
    };
  }

  /// Get a ClinicDetailsModel instance from mock data
  static ClinicDetailsModel getMockClinicDetailsModel() {
    final jsonData = getMockClinicDetails();
    return ClinicDetailsModel.fromJson(jsonData);
  }

  /// Get mock login data for test mode
  static Map<String, dynamic> getMockLogin() {
    return {
      "token": "mock-test-token-12345",
      "refreshToken": "mock-refresh-token-12345",
      "success": true
    };
  }

  /// Get a LoginModel instance from mock data
  static LoginModel getMockLoginModel() {
    final jsonData = getMockLogin();
    return LoginModel.fromJson(jsonData);
  }


}
