import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:gt_plus/modules/questionnaire/controller/aphab_questionnaire_controller.dart';
import 'package:gt_plus/utils/reusableWidgets/reusable_app_bar.dart';
import 'package:gt_plus/utils/reusableWidgets/reusable_footer.dart';
import 'package:gt_plus/utils/reusableWidgets/reusable_button.dart';
import 'package:gt_plus/utils/appConst/app_colors.dart';
import 'package:gt_plus/models/questionnaire_models.dart';

class APHABQuestionnaireView extends GetView<APHABQuestionnaireController> {
  const APHABQuestionnaireView({super.key});

  static const String routeName = "/APHABQuestionnaireView";

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: reusableAppBar(context: context),
      bottomNavigationBar: const ReusableFooter(),
      body: _buildBody(context),
    );
  }

  Widget _buildBody(BuildContext context) {
    return Obx(() {
      if (controller.isInitializing.value) {
        return const Center(child: CircularProgressIndicator());
      }

      return _buildContent(context);
    });
  }

  Widget _buildContent(BuildContext context) {
    final questionnaireSet = controller.getQuestionnaireSet();
    if (questionnaireSet == null) {
      return const Center(
        child: Text('Failed to load questionnaire data'),
      );
    }

    return SingleChildScrollView(
      child: Padding(
        padding: EdgeInsets.symmetric(horizontal: context.width * .04),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: 24),
            _buildHeader(context, questionnaireSet),
            const SizedBox(height: 20),
            _buildReadOnlyIndicator(),
            _buildDescription(context, questionnaireSet),
            const SizedBox(height: 24),
            _buildQuestions(context, questionnaireSet),
            const SizedBox(height: 32),
            _buildActionButtons(context),
            const SizedBox(height: 32),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader(BuildContext context, QuestionnaireSet questionnaireSet) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          questionnaireSet.label,
          style: const TextStyle(
            color: AppColors.charcoalBlue,
            fontSize: 24,
            fontWeight: FontWeight.bold,
          ),
        ),
      ],
    );
  }

  Widget _buildReadOnlyIndicator() {
    return Obx(() {
      if (controller.isReadOnly.value) {
        // Read-Only Mode Indicator
        return Container(
          margin: const EdgeInsets.only(bottom: 16),
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          decoration: BoxDecoration(
            color: Colors.blue.shade50,
            border: Border.all(color: Colors.blue.shade200),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Row(
            children: [
              Icon(
                Icons.lock_outline,
                color: Colors.blue.shade600,
                size: 20,
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  "Read-Only Mode: This questionnaire has been completed. Use 'Edit' to make changes or 'Reset' to start over.",
                  style: TextStyle(
                    color: Colors.blue.shade700,
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
        );
      } else if (controller.isFormCompleted.value &&
          !controller.isReadOnly.value) {
        // Edit Mode Indicator (for previously completed forms)
        return Container(
          margin: const EdgeInsets.only(bottom: 16),
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          decoration: BoxDecoration(
            color: Colors.orange.shade50,
            border: Border.all(color: Colors.orange.shade200),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Row(
            children: [
              Icon(
                Icons.edit_outlined,
                color: Colors.orange.shade600,
                size: 20,
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  "Edit Mode: You are now editing a previously completed questionnaire. Changes will be saved when you submit.",
                  style: TextStyle(
                    color: Colors.orange.shade700,
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
        );
      } else {
        return const SizedBox.shrink();
      }
    });
  }

  Widget _buildDescription(
      BuildContext context, QuestionnaireSet questionnaireSet) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.charcoalBlue.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Text(
        questionnaireSet.description,
        style: const TextStyle(
          color: AppColors.charcoalBlue,
          fontSize: 14,
          height: 1.5,
        ),
      ),
    );
  }

  Widget _buildQuestions(
      BuildContext context, QuestionnaireSet questionnaireSet) {
    if (questionnaireSet.data == null || questionnaireSet.data!.isEmpty) {
      return const SizedBox.shrink();
    }

    return Column(
      children: questionnaireSet.data!.asMap().entries.map((entry) {
        final index = entry.key;
        final section = entry.value;
        // APHAB has a flat structure where each "section" is actually a question
        return _buildAPHABQuestion(
            context, section, questionnaireSet, index + 1);
      }).toList(),
    );
  }

  Widget _buildAPHABQuestion(BuildContext context, QuestionnaireSection section,
      QuestionnaireSet questionnaireSet, int questionNumber) {
    // For APHAB, each section represents a single question
    return Container(
      margin: const EdgeInsets.only(bottom: 24),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey.shade300),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            // APHAB uses 'title' field for question text
            '$questionNumber. ${section.title ?? section.label}',
            style: const TextStyle(
              color: AppColors.charcoalBlue,
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 12),
          _buildAPHABAnswerWidget(context, section, questionnaireSet),
        ],
      ),
    );
  }

  Widget _buildAPHABAnswerWidget(BuildContext context,
      QuestionnaireSection section, QuestionnaireSet questionnaireSet) {
    // APHAB uses options at the questionnaire level
    if (questionnaireSet.options != null &&
        questionnaireSet.options!.isNotEmpty) {
      return _buildAPHABRadioAnswer(
          context, section, questionnaireSet.options!);
    }

    return const SizedBox.shrink();
  }

  Widget _buildAPHABRadioAnswer(BuildContext context,
      QuestionnaireSection section, List<QuestionnaireOption> options) {
    return Obx(() {
      final selectedValue = controller.getAnswer(section.value);

      return Wrap(
        spacing: 8.0,
        runSpacing: 4.0,
        children: options.map((option) {
          final isSelected = selectedValue == option.value;
          final isReadOnly = controller.isReadOnly.value;
          return GestureDetector(
            onTap: isReadOnly ? null : () {
              controller.updateAnswer(section.value, option.value);
            },
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
              decoration: BoxDecoration(
                color: isReadOnly
                    ? Colors.grey.shade200
                    : (isSelected ? Colors.blue.shade100 : Colors.grey.shade100),
                border: Border.all(
                  color: isReadOnly
                      ? Colors.grey.shade400
                      : (isSelected ? Colors.blue : Colors.grey.shade300),
                  width: isSelected ? 2 : 1,
                ),
                borderRadius: BorderRadius.circular(20),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(
                    isSelected
                        ? Icons.radio_button_checked
                        : Icons.radio_button_unchecked,
                    size: 16,
                    color: isReadOnly
                        ? Colors.grey.shade500
                        : (isSelected ? Colors.blue : Colors.grey),
                  ),
                  const SizedBox(width: 6),
                  Text(
                    option.label,
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w400,
                      color: isReadOnly
                          ? Colors.grey.shade600
                          : (isSelected ? Colors.blue.shade800 : Colors.black87),
                    ),
                  ),
                ],
              ),
            ),
          );
        }).toList(),
      );
    });
  }

  Widget _buildActionButtons(BuildContext context) {
    return Obx(() {
      if (controller.isReadOnly.value) {
        // Show Reset and Edit buttons when in read-only mode
        return Row(
          children: [
            ReusableButton(
              width: context.width * .22,
              title: 'Reset',
              color: Colors.red,
              borderColor: Colors.red,
              onTap: () {
                controller.resetForm();
              },
            ),
            const SizedBox(width: 16),
            ReusableButton(
              width: context.width * .22,
              title: 'Edit',
              onTap: () {
                controller.enableEditMode();
              },
            ),
          ],
        );
      } else {
        // Show Submit button when in edit mode
        return ReusableButton(
          width: context.width * .22,
          title: 'Submit',
          onTap: controller.submitQuestionnaire,
          isLoading: controller.isSubmitting.value,
        );
      }
    });
  }
}
