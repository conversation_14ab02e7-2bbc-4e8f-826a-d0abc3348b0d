import 'dart:io';
import 'package:gt_plus/utils/appConst/app_colors.dart';
import 'package:gt_plus/utils/appConst/app_images.dart';
import 'package:gt_plus/utils/reusableWidgets/resusable_snackbar.dart';
import 'package:gt_plus/utils/reusableWidgets/reusable_button.dart';
import 'package:gt_plus/utils/reusableWidgets/reusable_image_with_shimmer.dart';
import 'package:network_info_plus/network_info_plus.dart';
import 'package:open_settings_plus/core/open_settings_plus.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:gt_plus/modules/microscope/view/microscope_view.dart';

class WifiInstructionView extends StatelessWidget {
  static const String routeName = '/WifiInstructionView';

  const WifiInstructionView({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Skin'),
      ),
      body: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              const Text(
                'Connect to Wireless Microscopic Camera',
                style: TextStyle(
                  fontSize: 28,
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 20),
              const Text(
                'Please follow the steps below:',
                style: TextStyle(fontSize: 24),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 20),
              _buildInstructionStep(
                '1. Ensure the Wireless Microscopic Camera is turned on - Long press the "Power" button to turn it on',
              ),
              const SizedBox(height: 16),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  Expanded(
                      child: ReusableImageWithShimmer(
                    url: AppImages.ntMircoScope,
                    height: context.height * .25,
                    isCircle: false,
                    boxFit: BoxFit.contain,
                  )
                      // Image.asset(
                      //   AppImages.img,
                      //   height: 120,
                      //   fit: BoxFit.contain,
                      // ),
                      ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Image.asset(
                      AppImages.imgWifiInstruction,
                      height: context.height * .25,
                      fit: BoxFit.contain,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              _buildInstructionStep(
                '2. Click on the "Open Wi-Fi Settings" button below and connect to the "Max-see-xxxx" network',

              ),
              const SizedBox(height: 16),
              _buildInstructionStep(
                '3. After connecting to the microscopic camera, open the GT+ App and click on "Proceed" to capture the image',

              ),
              const SizedBox(height: 40),
              Row(
                children: [
                  Expanded(
                    child: ReusableButton(
                      title: "Open Wi-Fi Settings",
                      onTap: _openWifiSettings,
                      color: AppColors.orange,
                      borderColor: AppColors.orange,
                      suffixIcon: const Icon(
                        Icons.wifi,
                        color: Colors.white,
                      ),
                    ),
                  ),
                  const SizedBox(
                    width: 24,
                  ),
                  Expanded(
                    child: ReusableButton(
                      title: "Proceed",
                      onTap: _validateAndNavigate,
                      color: Colors.green,
                      borderColor: Colors.green,
                      suffixIcon: const Icon(
                        Icons.arrow_forward_ios_outlined,
                        color: Colors.white,
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Future<void> _validateAndNavigate() async {
    if (await _requestLocationPermission() == false) return;
    final String? wifiName = await _getWifiName();
    print("wifi name found $wifiName");
    if (wifiName != null) {
      final normalizedWifiName =
          wifiName.toLowerCase().replaceAll(RegExp(r'[^a-z]'), '');
      if (normalizedWifiName.contains('maxsee')) {
        Get.offNamed(MicroscopeView.routeName);
        return;
      }
    }
    reusableSnackBar(
        message: "Please connect to the Wifi containing MaxSee...");
  }

  Future<bool> _requestLocationPermission() async {
    if (!Platform.isAndroid && !Platform.isIOS) return true;
    final status = await Permission.location.request();
    if (!status.isGranted) {
      reusableSnackBar(message: "Location access is required to verify Wifi");

      return false;
    }
    return true;
  }

  Future<String?> _getWifiName() async {
    final networkInfo = NetworkInfo();
    String? wifiName = await networkInfo.getWifiName();
    return wifiName;
  }

  Future<void> _openWifiSettings() async {
    try {
      switch (OpenSettingsPlus.shared) {
        case OpenSettingsPlusAndroid settings:
          settings.wifi();
          break;
        case OpenSettingsPlusIOS settings:
          settings.wifi();
          break;
        default:
          reusableSnackBar(message: "Platform not supported");
      }
    } catch (e) {
      reusableSnackBar(message: "Could not open settings");
    }
  }

  Widget _buildInstructionStep(String text,) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            text,
            style: const TextStyle(fontSize: 24),
          ),
        ],
      ),
    );
  }
}
