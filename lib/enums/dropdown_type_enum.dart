enum DropdownTypesEnum {
  alcohol("assets/json/Alcohol.json"),
  bloodGroup("assets/json/BloodGroup.json"),
  coffeeTeaNumberofTimesInaDay("assets/json/CoffeeTeaNumberofTimesInaDay.json"),
  diagnosis("assets/json/Diagnosis.json"),
  drugs("assets/json/Drugs.json"),
  education("assets/json/Education.json"),
  ethnicity("assets/json/Ethnicity.json"),
  falls("assets/json/Falls.json"),
  hour("assets/json/Hour.json"),
  maritalStatus("assets/json/MaritalStatus.json"),
  meridiem("assets/json/Meridiem.json"),
  minute("assets/json/Minute.json"),
  nonVegetarian("assets/json/NonVegetarian.json"),
  numberMedications("assets/json/NumberMedications.json"),
  onDiabetesMedicationSince("assets/json/OnDiabetesMedicationSince.json"),
  onHypertensionMedicationSince(
      "assets/json/OnHypertensionMedicationSince.json"),
  onThyroidMedicationSince("assets/json/OnThyroidMedicationSince.json"),
  otherConditions("assets/json/OtherConditions.json"),
  prakriti("assets/json/Prakriti.json"),
  questionnaireData("assets/json/QuestionnaireData.json"),
  severity("assets/json/Severity.json"),
  smoke("assets/json/Smoke.json"),
  smokingDuration("assets/json/Smoking_duration.json"),
  smokingQuantity("assets/json/Smoking_quantity.json"),
  smokingSubstance("assets/json/Smoking_Substance.json"),
  states("assets/json/States.json"),
  timeSinceLastMeal("assets/json/TimeSinceLastMeal.json"),
  towns("assets/json/Towns.json"),
  gender("assets/json/gender.json");

  final String path;
  const DropdownTypesEnum(this.path);
}
